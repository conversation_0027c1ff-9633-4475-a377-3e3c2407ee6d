// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/typescript-node
{
	"name": "Node.js & TypeScript",
	// Or use a Dockerfile or Docker Compose file. More info: https://containers.dev/guide/dockerfile
	"build": {
    "dockerfile": "Dockerfile",
    "args": {
        "VARIANT": "latest"
    }
  },
  "postCreateCommand": "pnpm install",

	// Features to add to the dev container. More info: https://containers.dev/features.
	// "features": {},

	// Use 'forwardPorts' to make a list of ports inside the container available locally.
	"forwardPorts": [4200],

	// Use 'postCreateCommand' to run commands after the container is created.
	// "postCreateCommand": "yarn install",

	// Configure tool-specific properties.
	"customizations": {
    "vscode": {
      "settings": {
        "extensions.ignoreRecommendations": true
      },
      "extensions": [
        "ms-azuretools.vscode-docker",
        "mikael.angular-beastcode"
      ]
    }
  }

	// Uncomment to connect as root instead. More info: https://aka.ms/dev-containers-non-root.
	// "remoteUser": "root"
}
