@use '@angular/material' as mat;

// Layout
$gutter: 16px !default;

// Sidenav
$sidenav-width: 240px !default;
$sidenav-collapsed-width: 50px !default;
$sidenav-width-mobile: 280px !default;

// Toolbar
$toolbar-height-desktop: 64px !default;
$toolbar-height-mobile: 56px !default;

// Topmenu
$topmenu-sticky-position-desktop: $toolbar-height-desktop !default;
$topmenu-sticky-position-mobile: $toolbar-height-mobile !default;

// Typography
$font-family-sans-serif: 'Roboto', 'Helvetica Neue Light', 'Helvetica Neue', Helvetica, Arial, 'Lucida Grande', sans-serif !default;
$font-family-monospace: 'Roboto Mono', monospace !default;
$font-family-base: $font-family-sans-serif !default;

// Breakpoints
//
// Define the minimum dimensions at which your layout will change,
// adapting to different screen sizes, for use in media queries.
$breakpoints: (
  xsmall: 0,
  small:  600px,
  medium: 960px,
  large:  1280px,
  xlarge: 1920px
) !default;

// Material colors - simplified version without deprecated palette functions
$mat-colors: (
  red: #f44336,
  pink: #e91e63,
  purple: #9c27b0,
  deep-purple: #673ab7,
  indigo: #3f51b5,
  blue: #2196f3,
  light-blue: #03a9f4,
  cyan: #00bcd4,
  teal: #009688,
  green: #4caf50,
  light-green: #8bc34a,
  lime: #cddc39,
  yellow: #ffeb3b,
  amber: #ffc107,
  orange: #ff9800,
  deep-orange: #ff5722,
  brown: #795548,
  gray: #9e9e9e,
  blue-gray: #607d8b,
  white: white,
  black: black,
  light: white,
  dark: rgba(black, .87),
) !default;

// The material default animation curves
$swift-ease-out-duration: 400ms !default;
$swift-ease-out-timing-function: cubic-bezier(.25, .8, .25, 1) !default;
