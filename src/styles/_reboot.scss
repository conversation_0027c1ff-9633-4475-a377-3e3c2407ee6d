:root {
  --body-font-family: 'Roboto', 'Helvetica Neue Light', 'Helvetica Neue', Helvetica, Arial,
    'Lucida Grande', sans-serif;
  --code-font-family: 'Roboto Mono', monospace;
  --gutter: 1rem;
  --sidenav-width: 15rem;
  --sidenav-collapsed-width: 4rem;
  --body-background-color: var(--mat-sys-surface-container);
  --link-color: var(--mat-sys-primary);
  --link-hover-color: var(--mat-sys-primary);
  --code-border-color: light-dark(rgba(19, 28, 43, .08), rgba(218, 226, 249, .08));
  --code-background-color: light-dark(rgba(19, 28, 43, .04), rgba(218, 226, 249, .04));
  --header-background-color: light-dark(rgba(239, 237, 240, .8), rgba(31, 32, 34, .8));
  --user-panel-background-color: var(--mat-sys-surface-container);
  --user-panel-hover-background-color: var(--mat-sys-surface-variant);
  --sidemenu-heading-hover-background-color: var(--mat-sys-surface-container-highest);
  --sidemenu-active-heading-text-color: var(--mat-sys-primary);
  --sidemenu-active-heading-background-color: var(--mat-sys-primary-container);
  --sidemenu-active-heading-hover-background-color: var(--mat-sys-primary-container);
  --sidemenu-expanded-background-color: var(--mat-sys-surface-container);
  --topmenu-text-color: var(--mat-sys-on-background);
  --topmenu-background-color: var(--header-background-color);
  --topmenu-item-active-background-color: var(--mat-sys-primary-container);
  --topmenu-dropdown-item-active-text-color: var(--mat-sys-primary);
  --mat-toolbar-standard-height: 4rem;
  --mat-toolbar-mobile-height: 3.5rem;
}

*,
::after,
::before {
  box-sizing: border-box;
}

html,
body {
  position: relative; // 1
  height: 100%;
  overflow: auto; // 2
  background-color: var(--body-background-color);
}

body {
  margin: 0;
  font-family: var(--body-font-family);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
}

dl,
ol,
ul {
  margin-top: 0;
  margin-bottom: 1rem;
}

code,
kbd,
pre,
samp {
  font-family: var(--code-font-family);
}

code {
  padding: 0.125rem 0.25rem;
  font-size: 80%;
  overflow-wrap: break-word;
  background-color: var(--code-background-color);
  border: 1px solid var(--code-border-color);
  border-radius: 0.25rem;
}

a {
  color: var(--link-color);

  &:hover {
    color: var(--link-hover-color);
  }
}

@media (width <=720px) {
  .hide-small {
    display: none !important;
  }

  .show-small {
    display: block !important;
  }
}
