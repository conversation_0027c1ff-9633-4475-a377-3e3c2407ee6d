import { Routes } from '@angular/router';
// import { authGuard } from '@core';

import { AdminLayoutComponent } from '@/layouts/admin-layout/admin-layout.component';
import { DashboardComponent } from '@/pages/dashboard/dashboard.component';
// import { AuthLayoutComponent } from '@/layouts/auth-layout/auth-layout.component';
// import { LoginComponent } from '@/pages/sessions/login/login.component';
import { Error403Component } from '@/pages/exceptions/403.component';
import { Error404Component } from '@/pages/exceptions/404.component';
import { Error500Component } from '@/pages/exceptions/500.component';

export const routes: Routes = [{
  path: '',
  component: AdminLayoutComponent,
  // canActivate: [authGuard],
  // canActivateChild: [authGuard],
  children: [{
    path: '',
    redirectTo: 'dashboard',
    pathMatch: 'full'
  }, {
    path: 'dashboard',
    component: DashboardComponent,
    data: {
      title: 'Dashboard',
      titleI18n: 'dashboard',
    },
  }, {
    path: '403',
    component: Error403Component,
  }, {
    path: '404',
    component: Error404Component,
  }, {
    path: '500',
    component: Error500Component,
  }]
// }, {
//   path: 'auth',
//   component: AuthLayoutComponent,
//   children: [{
//     path: 'login',
//     component: LoginComponent
//   }],
}, {
  path: '**',
  redirectTo: 'dashboard',
}];
