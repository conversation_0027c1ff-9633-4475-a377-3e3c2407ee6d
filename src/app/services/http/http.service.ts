import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '@/envs/environment';

import { API_PATH } from './apis';

const ApiPath = (apiName: string): string => {
  console.log(environment);
  return `${environment.baseUrl}/${apiName}?t=${+(new Date())}`;
}

@Injectable({
  providedIn: 'root'
})
export class HttpService {
  constructor(
    private http: HttpClient,
  ) { }

  /**
   * 获取侧边栏菜单
   */
  getMenus(): Observable<any> {
    return this.http.get(ApiPath(API_PATH.menu));
  }
}
