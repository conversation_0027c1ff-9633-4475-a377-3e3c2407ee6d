// Long Shadow
//
// https://codepen.io/c_fitzmaurice/pen/ZYJeRY

@use 'sass:color';
@use 'sass:list';
@use 'sass:meta';
@use 'sass:map';
@use 'sass:math';

@function long-shadow($direction, $length, $color, $fade: false, $shadow-count: 100) {
  $shadows: ();
  $conversion-map: (
    to top: 180deg,
    to top right: 135deg,
    to right top: 135deg,
    to right: 90deg,
    to bottom right: 45deg,
    to right bottom: 45deg,
    to bottom: 0deg,
    to bottom left: 315deg,
    to left bottom: 315deg,
    to left: 270deg,
    to left top: 225deg,
    to top left: 225deg
  );

  @if map.has-key($conversion-map, $direction) {
    $direction: map.get($conversion-map, $direction);
  }

  @for $i from 1 through $shadow-count {
    $current-step: math.div($i * $length, $shadow-count);
    $current-color: if(
      not $fade,
      $color,
      if(
        meta.type-of($fade) == 'color',
        color.mix($fade, $color, math.div($i, $shadow-count) * 100%),
        color.rgba($color, 1 - math.div($i, $shadow-count))
      )
    );

    $shadows: list.append(
      $shadows,
      (math.sin(0deg + $direction) * $current-step)
      (math.cos(0deg + $direction) * $current-step)
      0
      $current-color,
      'comma'
    );
  }

  @return $shadows;
}
