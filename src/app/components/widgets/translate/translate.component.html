<button mat-icon-button class="lsx-toolbar-button" [matMenuTriggerFor]="menu">
  <mat-icon fontSet="material-symbols-rounded">translate</mat-icon>
</button>

<mat-menu #menu="matMenu">
  @for (lang of langs; track lang.value) {
    <button mat-menu-item (click)="changeLang(lang.value)">
      <span class="d-flex justify-content-between gap-8">
        {{ lang.name | translate }}
        @if (lang.value === options.language) {
          <mat-pseudo-checkbox state="checked" appearance="minimal" />
        }
      </span>
    </button>
  }
</mat-menu>
