import { Component, OnInit, ViewEncapsulation, inject } from '@angular/core';
import { Router, RouterLink } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';

import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';

import { AuthService, User } from '@/services';

@Component({
  selector: 'app-user-panel',
  templateUrl: './user-panel.component.html',
  styleUrls: ['./user-panel.component.scss'],
  encapsulation: ViewEncapsulation.None,
  imports: [
    RouterLink,
    MatButtonModule,
    MatIconModule,
    MatTooltipModule,
    TranslateModule,
  ],
})
export class UserPanelComponent implements OnInit {
  private readonly auth = inject(AuthService);

  user!: User;
  // user = {
  //   name: 'xxx',
  //   avatar: 'assets/images/avatars/02.svg',
  //   email: '<EMAIL>'
  // }

  ngOnInit(): void {
    this.auth.user().subscribe(user => this.user = user);
  }
}
