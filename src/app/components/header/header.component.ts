import { Component, EventEmitter, Input, Output, ViewEncapsulation } from '@angular/core';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

import { BrandingComponent } from '@/components/widgets/branding/branding.component';
import { FullscreenComponent } from '@/components/widgets/fullscreen/fullscreen.component';
import { TranslateComponent } from '@/components/widgets/translate/translate.component';
import { UserComponent } from '@/components/widgets/user/user.component';
// import { NotificationComponent } from '@/components/widgets/notification/notification.component';

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrl: './header.component.scss',
  host: {
    class: 'lsx-header',
  },
  encapsulation: ViewEncapsulation.None,
  imports: [
    MatToolbarModule,
    MatButtonModule,
    MatIconModule,
    BrandingComponent,
    FullscreenComponent,
    TranslateComponent,
    UserComponent,
    // NotificationComponent,
    // UserPanelComponent,
  ],
})
export class HeaderComponent {
  @Input() showToggle = true;
  @Input() showBranding = false;

  @Output() toggleSidenav = new EventEmitter<void>();
  @Output() toggleSidenavNotice = new EventEmitter<void>();
}
