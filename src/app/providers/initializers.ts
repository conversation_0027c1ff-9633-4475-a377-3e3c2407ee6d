import { APP_INITIALIZER } from '@angular/core';

// import { SanctumService } from './sanctum.service';
// export function SanctumServiceFactory(sanctumService: SanctumService) {
//   return () => sanctumService.load();
// }

import { TranslateLangService } from './translate-lang.service';
export function TranslateLangServiceFactory(translateLangService: TranslateLangService) {
  return () => translateLangService.load();
}

// import { StartupService } from './startup.service';
// export function StartupServiceFactory(startupService: StartupService) {
//   return () => startupService.load();
// }

export const appInitializerProviders = [{
//   provide: APP_INITIALIZER,
//   useFactory: SanctumServiceFactory,
//   deps: [SanctumService],
//   multi: true,
// },{
  provide: APP_INITIALIZER,
  useFactory: TranslateLangServiceFactory,
  deps: [TranslateLangService],
  multi: true,
// }, {
//   provide: APP_INITIALIZER,
//   useFactory: StartupServiceFactory,
//   deps: [StartupService],
//   multi: true,
}];
