import { Component, OnInit } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatButtonModule } from '@angular/material/button';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
  imports: [TranslateModule, MatButtonModule],
})
export class DashboardComponent implements OnInit {

  constructor(private translate: TranslateService) { }

  ngOnInit(): void {
    console.log('Dashboard component initialized');
    console.log('Current language:', this.translate.currentLang);
    console.log('Default language:', this.translate.defaultLang);

    // Test translation
    this.translate.get('user.profile').subscribe(translation => {
      console.log('Translation test - user.profile:', translation);
    });
  }

  switchLanguage(lang: string) {
    console.log('Switching to language:', lang);
    this.translate.use(lang).subscribe(() => {
      console.log('Language switched to:', lang);
    });
  }

}
