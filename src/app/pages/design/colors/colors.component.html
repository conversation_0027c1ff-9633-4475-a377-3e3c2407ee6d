<div fxLayout="row wrap" fxLayoutGap="16px grid">
  @for (color of colors; track trackByColor($index, color)) {
    <div
      fxFlex.gt-md="20"
      fxFlex.gt-sm="25"
      fxFlex.gt-xs="50"
      fxFlex="100"
      >
      <div class="overflow-hidden">
        <div class="p-8 text-capitalize text-{{ color.key }}-95 bg-{{ color.key }}-10">{{ color.key }}</div>

        @for (hue of color.value | keyvalue: keyAscOrder; track hue) {
          @if (hue.key > 0 && hue.key < 100) {
            <div
              class="p-8 bg-{{color.key + '-' + hue.key}}"
              [class.text-light]="hue.key <= 60"
              [class.text-dark]="hue.key > 60"
            >
              <span>{{ hue.key }}</span>
              <span fxFlex></span>
              <span>{{ hue.value }}</span>
            </div>
          }
        }
      </div>
    </div>
  }
</div>
