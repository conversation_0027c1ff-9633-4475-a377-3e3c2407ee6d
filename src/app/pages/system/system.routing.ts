import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { UserComponent } from './user/user.component';
import { RoleComponent } from './role/role.component';
import { MenuComponent } from './menu/menu.component';
import { PostComponent } from './post/post.component';
import { DeptComponent } from './dept/dept.component';
import { DictComponent } from './dict/dict.component';
import { ConfigComponent } from './config/config.component';
import { NoticeComponent } from './notice/notice.component';
import { OperlogComponent } from './operlog/operlog.component';
import { LoginInforComponent } from './login-infor/login-infor.component';

const routes: Routes = [
  { path: 'user', component: UserComponent },
  { path: 'role', component: RoleComponent },
  { path: 'menu', component: MenuComponent },
  { path: 'post', component: PostComponent },
  { path: 'dept', component: DeptComponent },
  { path: 'dict', component: DictComponent },
  { path: 'config', component: ConfigComponent },
  { path: 'notice', component: NoticeComponent },
  { path: 'operlog', component: OperlogComponent },
  { path: 'logininfor', component: LoginInforComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class SystemRoutingModule {}
