import { NgModule } from '@angular/core';
import { SharedModule } from '@/shared/shared.module';

import { SystemRoutingModule } from './system.routing';

import { UserComponent } from './user/user.component';
import { RoleComponent } from './role/role.component';
import { MenuComponent } from './menu/menu.component';
import { PostComponent } from './post/post.component';
import { DeptComponent } from './dept/dept.component';
import { DictComponent } from './dict/dict.component';
import { ConfigComponent } from './config/config.component';
import { NoticeComponent } from './notice/notice.component';
import { OperlogComponent } from './operlog/operlog.component';
import { LoginInforComponent } from './login-infor/login-infor.component';

const COMPONENTS: any[] = [
  UserComponent,
  RoleComponent,
  MenuComponent,
  PostComponent,
  DeptComponent,
  DictComponent,
  ConfigComponent,
  NoticeComponent,
  OperlogComponent,
  LoginInforComponent,
];
const COMPONENTS_DYNAMIC: any[] = [];

@NgModule({
  imports: [
    SharedModule,
    SystemRoutingModule,
  ],
  declarations: [
    ...COMPONENTS,
    ...COMPONENTS_DYNAMIC,
  ],
})
export class SystemModule {}
