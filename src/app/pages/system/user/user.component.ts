import { Component, OnInit } from '@angular/core';

import { USER, SearchForm, TableColumns } from './user.data';

@Component({
  selector: 'app-user',
  templateUrl: './user.component.html',
  styleUrls: ['./user.component.scss']
})
export class UserComponent implements OnInit {
  defaultColumns = [...TableColumns];
  tableData: any[] = [];
  total = 0;
  isLoading = false;

  query = {
    q: '',
    page: 1,
    limit: 10,
    sortby: 'created_at',
    order: 'desc'
  }

  constructor() { }

  ngOnInit(): void {
  }

}
